import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

export interface GlucoseRange {
  label: string;
  values: string[];
  color: string;
}

interface BloodGlucoseChartProps {
  title: string;
  headers: string[];
  data: GlucoseRange[];
}

const BloodGlucoseChart: React.FC<BloodGlucoseChartProps> = ({
  title,
  headers,
  data,
}) => {
  return (
    <View style={styles.container}>
      <Text style={styles.title}>{title}</Text>

      <View style={styles.row}>
        <Text style={[styles.cell, styles.header]}>{headers[0]}</Text>
        <Text style={[styles.cell, styles.header]}>{headers[1]}</Text>
        <Text style={[styles.cell, styles.header]}>{headers[2]}</Text>
        <Text style={[styles.cell, styles.header]}>{headers[3]}</Text>
      </View>

      {data.map((row, rowIndex) => (
        <View key={rowIndex} style={styles.row}>
          <Text
            style={[styles.cell, { fontStyle: 'italic', color: row.color }]}
          >
            {row.label}
          </Text>
          {row.values.map((value, colIndex) => (
            <Text
              key={colIndex}
              style={[
                styles.cell,
                {
                  backgroundColor: row.color + '33', // light background
                  color: '#000',
                },
              ]}
            >
              {value}
            </Text>
          ))}
        </View>
      ))}
    </View>
  );
};

export default BloodGlucoseChart;

const styles = StyleSheet.create({
  container: {
    padding: 16,
    borderRadius: 12,
    backgroundColor: '#fff',
    shadowColor: '#000',
    shadowOpacity: 0.05,
    shadowRadius: 8,
    shadowOffset: { width: 0, height: 2 },
    elevation: 3,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  row: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  cell: {
    flex: 1,
    textAlign: 'center',
    paddingVertical: 8,
    borderRadius: 6,
  },
  header: {
    backgroundColor: '#ddd',
    fontWeight: '600',
  },
});
