import React, { useState } from 'react';
import {
  TouchableOpacity,
  StyleSheet,
  FlatList,
  ViewStyle,
  TextStyle,
  Modal,
  View,
  Pressable,
} from 'react-native';
import { colors, fonts } from '../../theme/theme';
import AppText from './AppText';
import { ChevronDown, CrossStroke } from '../../assets/svgs';

interface ModalPickerOption {
  label: string;
  value: string;
}

interface Props {
  label: string;
  options: ModalPickerOption[];
  selectedValue?: string;
  onSelect: (value: string) => void;
  containerStyle?: ViewStyle;
  labelStyle?: TextStyle;
  valueStyle?: TextStyle;
  disabled?: boolean;
  placeholder?: string;
  error?: string;
}

const ModalPicker: React.FC<Props> = ({
  label,
  options,
  selectedValue,
  onSelect,
  containerStyle,
  labelStyle,
  valueStyle,
  disabled = false,
  placeholder,
  error,
}) => {
  const [isModalVisible, setIsModalVisible] = useState(false);

  const selectedOption = options.find(option => option.value === selectedValue);
  const hasValue = !!selectedValue;

  // Handle picker toggle
  const togglePicker = () => {
    if (disabled) return;
    setIsModalVisible(!isModalVisible);
  };

  // Handle option selection
  const handleSelect = (value: string) => {
    onSelect(value);
    setIsModalVisible(false);
  };

  // Handle modal close
  const handleModalClose = () => {
    setIsModalVisible(false);
  };

  return (
    <>
      {/* Main picker button */}
      <View style={[styles.container, containerStyle]}>
        <TouchableOpacity
          style={[
            styles.picker,
            isModalVisible && styles.pickerFocused,
            disabled && styles.pickerDisabled,
            error && styles.pickerError,
          ]}
          onPress={togglePicker}
          disabled={disabled}
          activeOpacity={0.7}
        >
          {/* Label - shows at top when value is selected */}
          {hasValue && (
            <AppText style={[styles.label, labelStyle]}>{label}</AppText>
          )}

          {/* Selected value or placeholder */}
          {hasValue ? (
            <AppText style={[styles.value, valueStyle]}>
              {selectedOption?.label || selectedValue}
            </AppText>
          ) : (
            <AppText style={styles.placeholder}>{placeholder || label}</AppText>
          )}

          {/* Arrow icon */}
          <View style={styles.iconContainer}>
            <ChevronDown stroke={colors.grey_30} />
          </View>
        </TouchableOpacity>

        {/* Error message */}
        {!!error && (
          <View style={styles.errorContainer}>
            <View style={styles.errorIconContainer}>
              <CrossStroke width={16} height={16} stroke={colors.white} />
            </View>
            <AppText style={styles.errorText}>{error}</AppText>
          </View>
        )}
      </View>

      {/* Modal with options */}
      <Modal
        visible={isModalVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={handleModalClose}
      >
        <Pressable style={styles.modalOverlay} onPress={handleModalClose}>
          <View style={styles.modalContent}>
            {/* Modal header */}
            {/* <View style={styles.modalHeader}>
              <AppText style={styles.modalTitle}>{label}</AppText>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={handleModalClose}
              >
                <CrossStroke width={20} height={20} stroke={colors.grey_60} />
              </TouchableOpacity>
            </View> */}

            {/* Options list */}
            <FlatList
              data={options}
              keyExtractor={item => item.value}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={[
                    styles.option,
                    item.value === selectedValue && styles.selectedOption,
                  ]}
                  onPress={() => handleSelect(item.value)}
                  activeOpacity={0.7}
                >
                  <AppText
                    style={[
                      styles.optionText,
                      item.value === selectedValue && styles.selectedOptionText,
                    ]}
                  >
                    {item.label}
                  </AppText>
                </TouchableOpacity>
              )}
              showsVerticalScrollIndicator={true}
              style={styles.optionsList}
            />
          </View>
        </Pressable>
      </Modal>
    </>
  );
};

export default ModalPicker;

const styles = StyleSheet.create({
  container: {
    // marginBottom: 16,
  },
  picker: {
    backgroundColor: colors.background_color,
    paddingHorizontal: 16,
    minHeight: 58,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: colors.grey_05,
    position: 'relative',
    justifyContent: 'center',
  },
  pickerFocused: {
    borderColor: colors.primary,
  },
  pickerDisabled: {
    opacity: 0.6,
  },
  pickerError: {
    borderColor: colors.warning,
  },
  label: {
    fontSize: 12,
    fontFamily: fonts.NotoSans_Regular,
    color: colors.grey_30,
  },
  value: {
    fontSize: 12,
    color: colors.grey_80,
    paddingLeft: 6,
    fontFamily: fonts.NotoSans_Regular,
  },
  placeholder: {
    fontSize: 14,
    fontFamily: fonts.NotoSans_Regular,
    color: colors.light_Secondary,
    lineHeight: 20,
  },
  iconContainer: {
    position: 'absolute',
    right: 16,
    width: 16,
    height: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 5,
    gap: 9,
  },
  errorIconContainer: {
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: colors.warning,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    color: colors.warning,
    fontSize: 12,
    fontFamily: fonts.NotoSans_Medium,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: colors.white,
    borderRadius: 12,
    width: '85%',
    maxHeight: '70%',
    paddingVertical: 20,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: colors.grey_20,
  },
  modalTitle: {
    fontSize: 18,
    fontFamily: fonts.NotoSans_SemiBold,
    color: colors.grey_90,
  },
  closeButton: {
    padding: 4,
  },
  optionsList: {
    paddingHorizontal: 20,
  },
  option: {
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginVertical: 2,
    borderWidth: 1,
    borderColor: colors.grey_20,
  },
  selectedOption: {
    backgroundColor: colors.white,
    borderColor: colors.primary,
  },
  optionText: {
    fontSize: 14,
    fontFamily: fonts.NotoSans_Regular,
    color: colors.grey_30,
    lineHeight: 20,
  },
  selectedOptionText: {
    color: colors.primary,
    fontFamily: fonts.NotoSans_Medium,
  },
});
