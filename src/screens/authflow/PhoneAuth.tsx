import React from 'react';
import { Button, StyleSheet, View } from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { appStyles, colors, sizes } from '../../theme/theme';
import {
  AppButton,
  AppText,
  FormInput,
  PhonePicker,
  SocialButton,
} from '../../componets';
import { FacebookLogo, GoogleLogo } from '../../assets/svgs';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { StackParamList } from '../../navigations/StackNavigator';
import { useAppDispatch } from '../../redux/config/Store';
import { setUser } from '../../redux/slices/authSlice';
type Props = NativeStackScreenProps<StackParamList, 'PhoneAuth'>;

const PhoneAuth: React.FC<Props> = ({ navigation }) => {
  const insets = useSafeAreaInsets();
  // Phone validation schema using basic pattern
  const PhoneSchema = Yup.object().shape({
    phone: Yup.string().email('Invalid email').required('Email is required.'),
  });

  const formik = useFormik({
    initialValues: { phone: '' },
    validationSchema: PhoneSchema,
    onSubmit: values => {
      navigation.navigate('PhoneCodeVerification', { phone: values.phone });
    },
  });

  const {
    handleChange,
    handleBlur,
    handleSubmit,
    values,
    errors,
    touched,
    isValid,
    dirty,
  } = formik;
  const dispatch = useAppDispatch();
  return (
    <KeyboardAwareScrollView
      contentContainerStyle={styles.scrollContent}
      keyboardShouldPersistTaps="handled"
      showsVerticalScrollIndicator={false}
    >
      <View style={[styles.container, { paddingBottom: insets.bottom + 20 }]}>
        <View>
          <AppText style={appStyles.largeTitle}>Sign up</AppText>
          <AppText pt={20} style={appStyles.h1}>
            Hello!
          </AppText>
          <AppText pt={12} style={appStyles.body3}>
            Enter your Email
          </AppText>
          {/* <Button
            title="press"
            onPress={() => dispatch(setUser({ id: '1111-2222' }))}
          /> */}
          {/* Phone input with Formik */}
          {/* <PhonePicker
            countryCode="+237"
            placeholder="123 45 67 89"
            value={values.phone}
            onChangeText={handleChange('phone')}
            onBlur={handleBlur('phone')}
            error={touched.phone && errors.phone ? errors.phone : ''}
          /> */}
          <FormInput
            label="Email"
            value={values.phone}
            onChangeText={handleChange('phone')}
            onBlur={handleBlur('phone')}
            keyboardType="email-address"
            error={touched.phone ? errors.phone : ''}
            containerStyle={{ marginTop: 12 }}
          />
          <AppText mt={12} style={[appStyles.body2, { color: colors.grey_80 }]}>
            We will send you confirmation code
          </AppText>
        </View>

        <View>
          {/* OR separator */}
          <View style={appStyles.orContainer}>
            <View style={appStyles.horizontalLine} />
            <AppText px={12} style={appStyles.body3}>
              Or
            </AppText>
            <View style={appStyles.horizontalLine} />
          </View>

          {/* Social buttons */}
          <SocialButton
            title="Continue with Google"
            icon={<GoogleLogo />}
            containerStyle={styles.socialButtonSpacing}
            onPress={() => {}}
          />
          <SocialButton
            title="Continue with Facebook"
            icon={<FacebookLogo />}
            containerStyle={styles.facebookButtonSpacing}
            onPress={() => {}}
          />

          {/* Submit button */}
          <AppButton
            title="Create"
            onPress={handleSubmit}
            containerStyle={styles.loginButton}
            disabled={!isValid || !dirty}
          />

          {/* Optional sign-up link */}
          <AppText style={appStyles.footerText}>
            Already have an account?{' '}
            <AppText
              onPress={() => navigation.replace('Login')}
              style={appStyles.signupText}
            >
              Sign in{' '}
            </AppText>
          </AppText>
        </View>
      </View>
    </KeyboardAwareScrollView>
  );
};

export default PhoneAuth;

const styles = StyleSheet.create({
  scrollContent: {
    flexGrow: 1,
  },
  container: {
    flex: 1,
    justifyContent: 'space-between',
    backgroundColor: colors.background_color,
    paddingTop: 12,
    paddingHorizontal: sizes.paddingHorizontal,
  },
  socialButtonSpacing: {
    marginTop: 24,
  },
  facebookButtonSpacing: {
    marginTop: 16,
  },
  loginButton: {
    marginTop: 36,
  },
});
