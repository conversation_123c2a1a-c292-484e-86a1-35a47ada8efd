import { ScrollView, StatusBar, StyleSheet, Text, View } from 'react-native';
import React, { useState } from 'react';
import { colors, fonts, sizes } from '../../../theme/theme';
import TopAnimatedTab from '../../../componets/common/TopAnimatedTab';
import {
  BloodGlucoseChart,
  EstimatedA1C,
  GlucoseLineChart,
} from '../../../componets';

const BloodGlucose = () => {
  const [activeTab, setActiveTab] = useState('Logs');
  const TABS = ['Logs', 'Graph'];
  const headers = [
    'Mg/DL',
    'Fasting',
    'After Eating',
    '2-3 hours after Eating',
  ];

  const data = [
    {
      label: 'Normal',
      values: ['80-100', '170-200', '120-140'],
      color: '#71d3b4',
    },
    {
      label: 'Impaired Glucose',
      values: ['101-125', '190-230', '140-160'],
      color: '#facc6b',
    },
    {
      label: 'Diabetic',
      values: ['126+', '220-230', '200+'],
      color: '#f08080',
    },
  ];

  return (
    <View style={styles.container}>
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{
          backgroundColor: colors.background_color,
          paddingBottom: 34,
        }}
      >
        <StatusBar
          backgroundColor={colors.background_color}
          barStyle={'dark-content'}
        />
        <View style={{ paddingHorizontal: sizes.paddingHorizontal }}>
          <TopAnimatedTab
            activeTab={activeTab}
            setActiveTab={setActiveTab}
            TABS={TABS}
            containerStyle={{ marginTop: 12 }}
          />
        </View>

        <GlucoseLineChart />
        <EstimatedA1C />
        <BloodGlucoseChart
          title="Blood Glucose Chart"
          headers={headers}
          data={data}
        />
      </ScrollView>
    </View>
  );
};

export default BloodGlucose;

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.background_color,
    flex: 1,
  },
});
