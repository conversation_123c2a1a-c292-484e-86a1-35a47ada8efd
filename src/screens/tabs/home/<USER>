import React, { useEffect, useRef, useState } from 'react';
import {
  View,
  StyleSheet,
  StatusBar,
  TouchableOpacity,
  Animated,
  Easing,
} from 'react-native';
import { colors, fonts, sizes } from '../../../theme/theme';
import {
  AppButton,
  AppText,
  BottomSheet,
  HeaderLeft,
} from '../../../componets';
import { BottomSheetRef } from '../../../componets/common/BottomSheet';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { StackParamList } from '../../../navigations/StackNavigator';
import ButtonwithIcon from '../../../componets/common/BittonWithIcon';
import {
  DeviceSearchIcon,
  GradeintCircle,
  GradientOuterCircle,
  QuestionMarkCircle,
} from '../../../assets/svgs';

type Props = NativeStackScreenProps<StackParamList, 'MeasureHealthMetrices'>;

const MeasureHealthMetrices: React.FC<Props> = ({ navigation }) => {
  const [isSearching, setIsSearching] = useState(false);
  const rotateValue = new Animated.Value(0);
  const contentFadeValue = useRef(new Animated.Value(1)).current;
  const bottomSheetRef = useRef<BottomSheetRef>(null);

  useEffect(() => {
    navigation.setOptions({
      headerLeft: () => (
        <HeaderLeft
          onPress={() =>
            !isSearching ? navigation.goBack() : setIsSearching(false)
          }
        />
      ),
      headerTitle: isSearching ? 'Other Device' : 'Measure Your Blood Glucose',
    });
  }, [isSearching, navigation]);

  // Rotation animation for outer circle
  useEffect(() => {
    let animationLoop: Animated.CompositeAnimation;

    if (isSearching) {
      rotateValue.setValue(0);
      animationLoop = Animated.loop(
        Animated.timing(rotateValue, {
          toValue: 1,
          duration: 3000,
          easing: Easing.linear,
          useNativeDriver: true,
        }),
      );
      animationLoop.start();
    } else {
      rotateValue.stopAnimation();
      rotateValue.setValue(0);
    }

    return () => {
      if (animationLoop) {
        animationLoop.stop();
      }
      rotateValue.stopAnimation();
    };
  }, [isSearching, rotateValue]);

  // Content transition animation - only for circle content
  useEffect(() => {
    Animated.timing(contentFadeValue, {
      toValue: 0,
      duration: 150,
      useNativeDriver: true,
    }).start(() => {
      // After fade out, fade back in with new content
      Animated.timing(contentFadeValue, {
        toValue: 1,
        duration: 150,
        useNativeDriver: true,
      }).start();
    });
  }, [isSearching, contentFadeValue]);

  const startSearching = () => {
    setIsSearching(true);
  };

  const rotate = rotateValue.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  const renderDeviceIcon = () => {
    return (
      <View style={styles.searchIcon}>
        <View style={styles.phoneIcon}>
          <View style={styles.phoneScreen} />
          <View style={styles.phoneButton} />
        </View>
        <View style={styles.searchIconDot} />
      </View>
    );
  };

  const renderContent = () => {
    if (isSearching) {
      return <DeviceSearchIcon />;
    }
    return (
      <AppText style={styles.connectButtonText}>Connect your Device</AppText>
    );
  };

  return (
    <View style={styles.container}>
      <StatusBar
        backgroundColor={colors.background_color}
        barStyle="dark-content"
      />

      {!isSearching ? (
        <View style={styles.howToMeasureContainer}>
          <ButtonwithIcon
            label="How to measure"
            rightIcon={<QuestionMarkCircle />}
            labelStyle={styles.howToMeasureText}
            containerStyle={styles.howToMeasureContainer}
          />
        </View>
      ) : (
        <View style={{ marginTop: 100 }} />
      )}

      {/* Main Content */}
      <View style={styles.content}>
        {/* Circular Container with Layered Circles */}
        <TouchableOpacity
          style={styles.circularContainer}
          onPress={startSearching}
          activeOpacity={0.8}
          disabled={isSearching}
        >
          {/* Animated Outer Circle - only rotates when searching */}
          <Animated.View
            style={[
              styles.outerCircleWrapper,
              {
                transform: [{ rotate }],
              },
            ]}
          >
            <GradientOuterCircle />
          </Animated.View>

          {/* Inner Circle - positioned absolutely to be centered */}
          <View style={styles.innerCircleWrapper}>
            <GradeintCircle />
          </View>

          {/* Content - positioned absolutely on top of both circles with fade animation */}
          <View style={[styles.contentWrapper]}>{renderContent()}</View>
        </TouchableOpacity>

        {/* How to activate pairing mode link */}
        {isSearching && (
          <>
            <View style={styles.searchStatusContainer}>
              <AppText style={styles.searchTitle}>
                Searching for device...
              </AppText>
              <AppText style={styles.searchSubtitle}>
                Make sure your device is close to your phone and has pairing
                mode on.
              </AppText>
            </View>
            <TouchableOpacity
              style={styles.pairingModeContainer}
              onPress={() => bottomSheetRef.current?.present()}
            >
              <AppText style={styles.pairingModeText}>
                How to activate pairing mode?
              </AppText>
            </TouchableOpacity>
          </>
        )}
      </View>

      {/* Bottom Buttons */}
      {!isSearching && (
        <View style={styles.bottomButtons}>
          <AppButton
            title="Add Record Manually"
            containerStyle={styles.primaryButton}
            onPress={() => {
              // Handle manual record addition
            }}
          />
          <AppButton
            title="Scan with camera"
            containerStyle={styles.secondaryButton}
            titleStyle={styles.secondaryButtonText}
            onPress={() => {
              // Handle camera scan
            }}
          />
        </View>
      )}
      <BottomSheet
        ref={bottomSheetRef}
        title="How to activate pairing mode?"
        snapPoints={['75%']}
        showCloseButton={true}
        onClose={() => bottomSheetRef.current?.dismiss()}
        titleStyle={styles.bottomSheetTitle}
      >
        <View style={styles.bottomSheetContent}>
          {/* Step 1 */}
          <View style={styles.stepContainer}>
            <View style={styles.stepNumber}>
              <AppText style={styles.stepNumberText}>1</AppText>
            </View>
            <AppText style={styles.stepText}>
              Make sure that you have enabled Bluetooth on your phone. you can
              do this by going to your phone's settings and turning Bluetooth
              on.
            </AppText>
          </View>

          {/* Step 2 */}
          <View style={styles.stepContainer}>
            <View style={styles.stepNumber}>
              <AppText style={styles.stepNumberText}>2</AppText>
            </View>
            <AppText style={styles.stepText}>
              Once Bluetooth is enabled, make sure to turn on your physical
              device according to its manufacturer's instructions.
            </AppText>
          </View>

          {/* Step 3 */}
          <View style={styles.stepContainer}>
            <View style={styles.stepNumber}>
              <AppText style={styles.stepNumberText}>3</AppText>
            </View>
            <AppText style={styles.stepText}>
              As soon as the device is on MyDiabetes app with start searching
              for it. If the device is found, it will be shown on the list.
            </AppText>
          </View>

          {/* Step 4 */}
          <View style={styles.stepContainer}>
            <View style={styles.stepNumber}>
              <AppText style={styles.stepNumberText}>4</AppText>
            </View>
            <AppText style={styles.stepText}>
              There may be issues when connecting not a fully supported device.
              If you hae any questions regarding not supported devices you can
              always contact our customer support.
            </AppText>
          </View>
        </View>
      </BottomSheet>
    </View>
  );
};

export default MeasureHealthMetrices;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background_color,
  },

  howToMeasureContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    paddingHorizontal: sizes.paddingHorizontal,
    marginTop: 8,
  },
  howToMeasureText: {
    fontSize: 14,
    fontFamily: fonts.NotoSans_Regular,
    color: colors.grey_50,
    marginRight: 4,
  },
  questionMark: {
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: colors.grey_50,
    alignItems: 'center',
    justifyContent: 'center',
  },
  questionMarkText: {
    fontSize: 10,
    fontFamily: fonts.NotoSans_Medium,
    color: colors.white,
  },
  content: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: sizes.paddingHorizontal,
    position: 'relative',
  },
  circularContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    width: 241,
    height: 240,
    position: 'relative',
    marginBottom: 40,
  },
  outerCircleWrapper: {
    position: 'absolute',
    width: 241,
    height: 240,
    alignItems: 'center',
    justifyContent: 'center',
  },
  innerCircleWrapper: {
    position: 'absolute',
    width: 201,
    height: 200,
    alignItems: 'center',
    justifyContent: 'center',
    top: '50%',
    left: '50%',
    marginTop: -100, // Half of height (200/2)
    marginLeft: -100.5, // Half of width (201/2)
  },
  contentWrapper: {
    position: 'absolute',
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    height: '100%',
    zIndex: 10,
  },
  connectButton: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 20,
  },
  connectButtonText: {
    fontSize: 16,
    fontFamily: fonts.NotoSans_SemiBold,
    color: colors.grey_80,
    textAlign: 'center',
  },
  searchIcon: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  phoneIcon: {
    width: 40,
    height: 60,
    borderWidth: 2,
    borderColor: colors.grey_80,
    borderRadius: 8,
    position: 'relative',
    backgroundColor: colors.white,
  },
  phoneScreen: {
    flex: 1,
    margin: 4,
    backgroundColor: '#E8F4FD',
    borderRadius: 4,
  },
  phoneButton: {
    width: 16,
    height: 3,
    backgroundColor: colors.grey_80,
    borderRadius: 2,
    alignSelf: 'center',
    marginBottom: 4,
  },
  searchIconDot: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: colors.grey_80,
    backgroundColor: 'transparent',
    position: 'absolute',
    right: -5,
    bottom: -5,
  },
  searchStatusContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  searchTitle: {
    fontSize: 20,
    fontFamily: fonts.Catamaran_SemiBold,
    color: colors.grey_80,
    marginBottom: 12,
  },
  searchSubtitle: {
    fontSize: 14,
    fontFamily: fonts.NotoSans_Regular,
    color: colors.grey_60,
    textAlign: 'center',
    lineHeight: 20,
    paddingHorizontal: 20,
  },
  pairingModeContainer: {
    marginTop: 20,
  },
  pairingModeText: {
    fontSize: 14,
    fontFamily: fonts.NotoSans_Medium,
    color: colors.primary,
    textDecorationLine: 'underline',
  },
  bottomButtons: {
    paddingHorizontal: sizes.paddingHorizontal,
    paddingBottom: 20,
    gap: 12,
  },
  primaryButton: {
    backgroundColor: colors.primary,
  },
  secondaryButton: {
    backgroundColor: colors.white,
    borderWidth: 1,
    borderColor: colors.primary,
  },
  secondaryButtonText: {
    color: colors.primary,
  },
  // Bottom Sheet Styles
  bottomSheetContent: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  bottomSheetTitle: {
    color: colors.grey_90,
    fontFamily: fonts.NotoSans_SemiBold,
  },
  stepContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 24,
  },
  stepNumber: {
    width: 24,
    height: 24,
    borderRadius: 12,
    // backgroundColor: colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 4,
    marginTop: 2,
  },
  stepNumberText: {
    fontSize: 14,
    fontFamily: fonts.NotoSans_SemiBold,
    color: colors.primary,
  },
  stepText: {
    flex: 1,
    fontSize: 14,
    fontFamily: fonts.NotoSans_Regular,
    color: colors.grey_30,
    lineHeight: 20,
  },
});
