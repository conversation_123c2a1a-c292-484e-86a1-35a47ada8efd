import React from 'react';
import {
  ScrollView,
  StatusBar,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import { DrawerNavigationProp } from '@react-navigation/drawer';
import { StackParamList } from '../../../navigations/StackNavigator';
import { appStyles, colors, sizes } from '../../../theme/theme';
import {
  AppButton,
  AppText,
  HealthCard,
  HomeScreenHeader,
  InviteCard,
} from '../../../componets';
import { healthData } from '../../../constants/HealthData';

type Props = {
  navigation: DrawerNavigationProp<StackParamList, 'Home'>;
};

const Home: React.FC<Props> = ({ navigation }) => {
  return (
    <View style={styles.container}>
      {/* StatusBar styled to match primary theme */}
      <StatusBar backgroundColor={colors.primary} barStyle="light-content" />

      {/* Header with profile info */}
      <HomeScreenHeader
        name="<PERSON><PERSON><PERSON>"
        onPressIcon={() => navigation.openDrawer()}
      />

      {/* Row with section title and "See all" action */}
      <View style={styles.rowContainer}>
        <AppText style={styles.measurementTitle}>Measurements</AppText>
        <TouchableOpacity activeOpacity={0.5}>
          <AppText style={styles.seeAllText}>See all</AppText>
        </TouchableOpacity>
      </View>

      {/* Horizontal scrollable health cards */}
      <View>
        <ScrollView
          horizontal
          contentContainerStyle={styles.healthScrollContainer}
          showsHorizontalScrollIndicator={false}
        >
          {healthData.map((item, index) => (
            <HealthCard
              key={index}
              title={item.title}
              icon={<item.icon />}
              value={item.value}
              unit={item.unit}
              subtitle={item.subtitle}
              onPressCard={val => {
                if (val === 'Blood Glucose')
                  navigation.navigate('BloodGlucose');
              }}
            />
          ))}
        </ScrollView>
      </View>
      {/* Temporary button to Measure BloodGlucose screen */}
      <AppButton
        containerStyle={{ margin: 25 }}
        title="Measure Blood Glucose"
        onPress={() => navigation.navigate('MeasureHealthMetrices')}
      />
      {/* Temporary button to MyDevices screen */}
      <AppButton
        containerStyle={{ margin: 25, marginTop: 10 }}
        title="My Devices"
        onPress={() => navigation.navigate('MyDevices')}
      />
      {/* Invite section card */}
      <InviteCard
        title="Invite your friend"
        paragraph="Invite your friend and share your experience to your friends"
        containerStyle={styles.inviteCardStyle}
      />
    </View>
  );
};

export default Home;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background_color,
  },
  rowContainer: {
    ...appStyles.flexBtw,
    paddingHorizontal: sizes.paddingHorizontal,
    paddingTop: 16,
  },
  measurementTitle: {
    ...appStyles.h3,
    color: colors.grey_100,
  },
  seeAllText: {
    ...appStyles.body2,
    fontSize: 12,
    color: colors.primary,
  },
  healthScrollContainer: {
    gap: 6,
    paddingHorizontal: sizes.paddingHorizontal,
    paddingTop: 12,
  },
  inviteCardStyle: {
    backgroundColor: '#D1E6FF',
    marginTop: 24,
    marginHorizontal: sizes.paddingHorizontal,
  },
});
