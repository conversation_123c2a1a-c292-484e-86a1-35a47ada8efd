import { Platform, ScrollView, StyleSheet, View } from 'react-native';
import React from 'react';
import { appStyles, colors, sizes } from '../../../theme/theme';
import {
  AppButton,
  AppText,
  HorizontalLine,
  ProfileHeader,
  SettingItem,
} from '../../../componets';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { StackParamList } from '../../../navigations/StackNavigator';
import {
  BellStroke,
  CustomerSupportStroke,
  FAQsStroke,
  HelpSupportStroke,
  LockStroke,
  LogOutStroke,
  PaymentStroke,
  UserStroke,
} from '../../../assets/svgs';
import BottomSheet, {
  BottomSheetRef,
} from '../../../componets/common/BottomSheet';
import { useAppDispatch } from '../../../redux/config/Store';
import { logoutUser } from '../../../redux/slices/authSlice';

type Props = NativeStackScreenProps<StackParamList, 'Settings'>;

const Settings: React.FC<Props> = ({ navigation }) => {
  const dispatch = useAppDispatch();
  const bottomSheetRef = React.useRef<BottomSheetRef>(null);

  return (
    <View style={styles.container}>
      <ScrollView
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Top profile section */}
        <ProfileHeader onPress={() => {}} />

        {/* Separator */}
        <HorizontalLine marginTop={12} />

        {/* Settings Header */}
        <AppText style={styles.headerText}>Settings</AppText>

        {/* Settings Options */}
        <SettingItem
          label="Account settings"
          icon={<UserStroke />}
          containerStyle={styles.settingItemSpacing}
          onPressItem={() => navigation.navigate('AccountSettings')}
        />
        <SettingItem
          label="Payments and subscription"
          icon={<PaymentStroke />}
        />
        <SettingItem label="Privacy and sharing" icon={<LockStroke />} />
        <SettingItem label="Daily Reminder" icon={<BellStroke />} />
        <SettingItem label="Data synchronization" icon={<BellStroke />} />

        {/* Support Section Header */}
        <AppText style={[styles.headerText, styles.supportSection]}>
          Support
        </AppText>

        {/* Support Options */}
        <SettingItem
          label="Help center"
          icon={<HelpSupportStroke />}
          onPressItem={() => navigation.navigate('HelpCenter')}
        />
        <SettingItem
          label="Customer support"
          icon={<CustomerSupportStroke />}
        />
        <SettingItem
          label="FAQs"
          icon={<FAQsStroke />}
          onPressItem={() => navigation.navigate('FAQs')}
        />
        <SettingItem label="Share VitaeChek" icon={<FAQsStroke />} />

        {/* Logout section */}
        <AppText style={[styles.headerText, styles.logoutText]}>Logout</AppText>
        <SettingItem
          label="Log Out"
          icon={<LogOutStroke width={20} height={20} />}
          onPressItem={() => bottomSheetRef.current?.present()}
        />
      </ScrollView>

      <BottomSheet ref={bottomSheetRef} title="Logout" snapPoints={['35%']}>
        <View style={{ paddingBottom: Platform.OS === 'android' ? 25 : 42 }}>
          <View style={styles.sheetContentContainer}>
            <AppText style={styles.sheetContentTitle}>
              Are you sure you want to logout?
            </AppText>
          </View>
          <View style={styles.logoutButtonContainer}>
            <AppButton
              containerStyle={[
                styles.logoutButton,
                { backgroundColor: colors.grey_10 },
              ]}
              title="Cancel"
              titleStyle={{ color: colors.grey_60 }}
              onPress={() => bottomSheetRef.current?.dismiss()}
            />
            <AppButton
              containerStyle={styles.logoutButton}
              title="Yes, Logout"
              onPress={() => {
                dispatch(logoutUser());
                bottomSheetRef.current?.dismiss();
              }}
            />
          </View>
        </View>
      </BottomSheet>
    </View>
  );
};

export default Settings;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background_color,
    paddingHorizontal: sizes.paddingHorizontal,
    paddingTop: 18,
  },
  scrollContent: {
    paddingBottom: 32,
  },
  headerText: {
    ...appStyles.h3,
    color: colors.grey_90,
  },
  settingItemSpacing: {
    marginTop: 6,
  },
  supportSection: {
    paddingTop: 18,
  },
  logoutText: {
    paddingTop: 18,
    textDecorationLine: 'underline',
  },
  logoutButtonContainer: {
    ...appStyles.flexBtw,
    gap: 9.5,
    flex: 1,
  },
  logoutButton: {
    width: '50%',
  },
  sheetContentContainer: {
    paddingTop: 20,
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingBottom: 30,
  },
  sheetContentTitle: {
    ...appStyles.body1,
    // color: colors.grey_60,
  },
});
