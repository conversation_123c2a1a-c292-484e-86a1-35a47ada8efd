import { createNativeStackNavigator } from '@react-navigation/native-stack';
import {
  Image,
  Platform,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import {
  AccountCreationFeedback,
  HealthAssessment,
  Login,
  Onboarding,
  PhoneAuth,
  PhoneCodeVerification,
  UserProfile,
} from '../screens/authflow';
import { appStyles, colors, sizes } from '../theme/theme';
import { images } from '../assets/images';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { AppText, HeaderLeft } from '../componets';
import { BackArrow } from '../assets/svgs';
import BottomNavigation from './BottomNavigation';
import {
  About,
  AccountSettings,
  ChangePassword,
  EditProfile,
  FAQs,
  HelpCenter,
  Settings,
  TermsAndConditions,
  PrivacyPolicy,
  BloodGlucose,
} from '../screens/tabs';
import HealthProfileDetail from '../screens/tabs/profile/HealthProfileDetail';
import { useAppSelector } from '../redux/config/Store';

export interface UserData {
  fullName?: string;
  lastName?: string;
  dob?: string;
  gender?: string;
  height?: string;
  weight?: string;
  race?: string;
  modeSelection?: string;
  avatarUri?: string;
}

export type StackParamList = {
  // AUTH FLOW
  Onboarding: undefined;
  Login: undefined;
  PhoneAuth: undefined;
  PhoneCodeVerification: { phone: string };
  UserProfile: undefined;
  AccountCreationFeedback: undefined;
  HealthAssessment: undefined;
  // BOTTOM TABS
  BottomNavigation: undefined;
  Home: undefined;
  Profile: undefined;
  MeasureHeartRate: undefined;
  Resources: undefined;
  Doctors: undefined;
  // STACK NAVIGATOR
  EditProfile: { userData?: UserData };
  Settings: undefined;
  AccountSettings: undefined;
  About: undefined;
  ChangePassword: undefined;
  TermsAndConditions: undefined;
  PrivacyPolicy: undefined;
  HelpCenter: undefined;
  FAQs: undefined;
  HealthProfileDetail: { title: string };
  BloodGlucose: undefined;
};

const Stack = createNativeStackNavigator<StackParamList>();

const AuthHeader = () => {
  const insets = useSafeAreaInsets();
  return (
    <View
      style={[
        styles.headerContainer,
        {
          paddingTop: Platform.OS === 'ios' ? insets.top : 16,
        },
      ]}
    >
      <Image resizeMode="contain" source={images.AppLogo} style={styles.logo} />
    </View>
  );
};

const CustomHeader = ({
  title,
  onPress,
}: {
  title: string;
  onPress: () => void;
}) => {
  const insets = useSafeAreaInsets();
  return (
    <View style={[styles.customHeaderContainer]}>
      <TouchableOpacity hitSlop={10} activeOpacity={0.5} onPress={onPress}>
        <BackArrow width={24} height={24} />
      </TouchableOpacity>
      <AppText style={appStyles.headerTitleStyle}>{title}</AppText>
    </View>
  );
};

const StackNavigator = () => {
  const { user } = useAppSelector(state => state.auth);
  return (
    <Stack.Navigator
      // initialRouteName="HealthAssessment"
      // initialRouteName="BottomNavigation"
      screenOptions={({ navigation }) => ({
        navigationBarColor: colors.background_color,
        headerLeft: () => <HeaderLeft onPress={() => navigation.goBack()} />,
        headerTitleStyle: appStyles.headerTitleStyle,
        headerShadowVisible: true,
        headerStyle: { backgroundColor: colors.background_color },
        headerTitleAlign: 'left',
      })}
    >
      {!user?.id ? (
        <>
          {/* ATUH STACK */}
          <Stack.Screen
            name="Onboarding"
            component={Onboarding}
            options={{ headerShown: false }}
          />
          <Stack.Screen
            name="Login"
            component={Login}
            options={{
              header: () => <AuthHeader />,
            }}
          />
          <Stack.Screen
            name="PhoneAuth"
            component={PhoneAuth}
            options={{
              header: () => <AuthHeader />,
            }}
          />
          <Stack.Screen
            name="PhoneCodeVerification"
            component={PhoneCodeVerification}
            options={{
              header: () => <AuthHeader />,
            }}
          />
          <Stack.Screen
            name="UserProfile"
            component={UserProfile}
            options={{
              header: () => <AuthHeader />,
            }}
          />
          <Stack.Screen
            name="AccountCreationFeedback"
            component={AccountCreationFeedback}
            options={{
              header: () => <AuthHeader />,
            }}
          />
          <Stack.Screen
            name="HealthAssessment"
            component={HealthAssessment}
            options={
              {
                // header: () => <CustomHeader title="Health Assessment" />,
              }
            }
          />
        </>
      ) : (
        <>
          {/* BOTTOM TABS */}
          <Stack.Screen
            name="BottomNavigation"
            component={BottomNavigation}
            options={{ headerShown: false }}
          />
          {/* STACK NAVIGATOR */}
          <Stack.Screen name="EditProfile" component={EditProfile} />
          <Stack.Screen name="Settings" component={Settings} />
          <Stack.Screen
            name="AccountSettings"
            component={AccountSettings}
            options={{
              headerTitle: 'Account Settings',
            }}
          />
          <Stack.Screen name="About" component={About} />
          <Stack.Screen
            name="ChangePassword"
            component={ChangePassword}
            options={{
              headerTitle: 'Change Password',
            }}
          />
          <Stack.Screen
            name="TermsAndConditions"
            component={TermsAndConditions}
            options={{ headerTitle: 'Terms & Conditions' }}
          />
          <Stack.Screen
            name="PrivacyPolicy"
            component={PrivacyPolicy}
            options={{ headerTitle: 'Privacy Policy' }}
          />
          <Stack.Screen
            name="HelpCenter"
            component={HelpCenter}
            options={{ headerTitle: 'Help Center' }}
          />
          <Stack.Screen
            name="FAQs"
            component={FAQs}
            options={{ headerTitle: 'FAQs' }}
          />
          <Stack.Screen
            name="HealthProfileDetail"
            component={HealthProfileDetail}
            options={({ route }) => ({
              headerTitle: route?.params?.title,
            })}
          />
          <Stack.Screen
            name="BloodGlucose"
            component={BloodGlucose}
            options={{ headerTitle: 'Blood Glucose' }}
          />
        </>
      )}
    </Stack.Navigator>
  );
};

export default StackNavigator;

const styles = StyleSheet.create({
  headerContainer: {
    backgroundColor: colors.background_color,
    paddingHorizontal: sizes.paddingHorizontal,
    justifyContent: 'flex-end',
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderColor: colors.grey_20,
  },

  logo: {
    width: 156,
    height: 30,
  },
  customHeaderContainer: {
    backgroundColor: colors.background_color,
    paddingHorizontal: sizes.paddingHorizontal,
    borderBottomWidth: 1,
    borderColor: colors.grey_20,
    flexDirection: 'row',
    alignItems: 'center',
    height: 62,
  },
});
