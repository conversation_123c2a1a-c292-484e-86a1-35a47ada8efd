import React from 'react';
import { DrawerContentScrollView } from '@react-navigation/drawer';
import { Platform, StyleSheet, View } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import {
  HorizontalLine,
  InviteCard,
  ProfileHeader,
  SettingItem,
} from '../componets';
import { colors, fonts, sizes } from '../theme/theme';
import {
  HeartFill,
  HomeFill,
  MessageFill,
  NotificationFill,
  ReminderStroke,
  ResourcesFil,
  UserFill,
} from '../assets/svgs';

const CustomDrawerContent = (props: any) => {
  return (
    <LinearGradient
      colors={['#0873B4', '#055A8D']}
      start={{ x: 0, y: 0 }}
      end={{ x: 0, y: 1 }}
      style={styles.gradientContainer}
    >
      <DrawerContentScrollView
        showsVerticalScrollIndicator={false}
        {...props}
        contentContainerStyle={styles.scrollContainer}
      >
        <View
          style={{
            paddingTop: Platform.OS === 'android' ? 28 : 16,
            paddingHorizontal: sizes.paddingHorizontal,
          }}
        >
          {/* === User Profile Header === */}
          <ProfileHeader
            size={62}
            nameStyle={styles.nameStyle}
            subTitleStyle={styles.subTitleStyle}
            isChevron={false}
          />

          {/* === Separator Line === */}
          <HorizontalLine marginTop={15} marginBottom={40} />

          {/* === Drawer Menu Items === */}
          <SettingItem
            label="Home"
            labelStyle={styles.labelStyle}
            icon={<HomeFill />}
            containerStyle={styles.itemContainer}
            chevronColor={colors.white}
          />
          <SettingItem
            label="Messages"
            labelStyle={styles.labelStyle}
            icon={<MessageFill />}
            containerStyle={styles.itemContainer}
            chevronColor={colors.white}
          />
          <SettingItem
            label="Notifications"
            labelStyle={styles.labelStyle}
            icon={<NotificationFill />}
            containerStyle={styles.itemContainer}
            chevronColor={colors.white}
          />
          <SettingItem
            label="Favorites"
            labelStyle={styles.labelStyle}
            icon={<HeartFill />}
            containerStyle={styles.itemContainer}
            chevronColor={colors.white}
          />
          <SettingItem
            label="Reminder"
            labelStyle={styles.labelStyle}
            icon={<ReminderStroke />}
            containerStyle={styles.itemContainer}
            chevronColor={colors.white}
          />
          <SettingItem
            label="Resources"
            labelStyle={styles.labelStyle}
            icon={<ResourcesFil />}
            containerStyle={styles.itemContainer}
            chevronColor={colors.white}
          />

          {/* === Highlighted Profile Section === */}
          <SettingItem
            label="My profile"
            labelStyle={styles.profileLabelStyle}
            icon={<UserFill />}
            containerStyle={styles.profileContainer}
            chevronColor={colors.primary}
          />

          {/* === Share App Invite Card === */}
          <InviteCard
            title="Share VitaeChek"
            paragraph="Invite your friend and share your experience to your friends"
            containerStyle={styles.inviteCardStyle}
          />
        </View>
      </DrawerContentScrollView>
    </LinearGradient>
  );
};

export default CustomDrawerContent;

// === STYLES ===
const styles = StyleSheet.create({
  gradientContainer: {
    flex: 1,
    // borderTopRightRadius: 26,
    // borderBottomRightRadius: 26,
    // backgroundColor: 'transparent',
  },
  scrollContainer: {},
  nameStyle: {
    color: colors.white,
    fontFamily: fonts.Catamaran_Medium,
    fontSize: 18,
  },
  subTitleStyle: {
    color: colors.white,
    fontSize: 15,
    fontFamily: fonts.Catamaran_Regular,
  },
  labelStyle: {
    fontSize: 18,
    color: colors.white,
    fontFamily: fonts.NotoSans_Regular,
    marginLeft: 8,
  },
  itemContainer: {
    borderColor: colors.white,
    borderBottomWidth: 0,
    paddingVertical: 14,
    marginTop: 15,
    paddingHorizontal: 6,
    borderRadius: 12,
  },
  profileLabelStyle: {
    fontSize: 18,
    color: colors.primary,
    fontFamily: fonts.NotoSans_Regular,
    marginLeft: 8,
  },
  profileContainer: {
    backgroundColor: colors.white,
    borderRadius: 12,
    paddingVertical: 14,
    marginTop: 15,
    paddingHorizontal: 6,
  },
  inviteCardStyle: {
    backgroundColor: '#D1E6FF',
    marginTop: 40,
  },
});
