import React from 'react';
import { createDrawerNavigator } from '@react-navigation/drawer';
import StackNavigator from './StackNavigator';
import { colors, sizes } from '../theme/theme';
import CustomDrawerContent from './CustomDrawerContent';

const Drawer = createDrawerNavigator();

const DrawerNavigator = () => {
  return (
    <Drawer.Navigator
      screenOptions={{
        headerShown: false,
        drawerStyle: {
          backgroundColor: colors.primary,
          width: sizes.width * 0.85,
        },
        overlayColor: 'transparent',
        swipeEnabled: false,
      }}
      drawerContent={props => <CustomDrawerContent {...props} />}
    >
      <Drawer.Screen name="HomeStack" component={StackNavigator} />
      {/* You can add more screens like Help, Settings, etc. here if needed */}
    </Drawer.Navigator>
  );
};

export default DrawerNavigator;
